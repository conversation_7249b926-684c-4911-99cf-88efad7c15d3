from pathlib import Path
from preprocess import preprocess_image
from engines.ppocr import PPOCR
from engines.pptable import PPTable
from postprocess import extract_structured_data
from meta_table import process_ppstructure
from config import PATH_DATA, PATH_OUTPUTS, PATH_TEMP



# PATH_MODELS = Path(r"C:\Users\<USER>\.paddlex\official_models")
PATH_MODELS = PATH_TEMP / 'models'
engine = PPTable(model_dir=PATH_MODELS)
# engine = PPOCR(model_dir=PATH_MODELS)


files = [f'receipt{idx}.jpg' for idx in range(1,21)]
files = ['receipt5.jpg']

for file in files:
    image_path = PATH_DATA / file

    # 1. 전처리
    # preprocessed_image = preprocess_image(image_path)
    preprocessed_image = str(image_path) # Bypass w/o preprocess

    # 2. OCR 수행    
    results = engine.predict(preprocessed_image)

    # Visualize the results and save the JSON results
    ouput_path = PATH_OUTPUTS / Path(file).stem
    Path(ouput_path).mkdir(parents=True, exist_ok=True)

    for res in results:
        res.print()
        res.save_to_img(ouput_path)
        res.save_to_json(ouput_path)

    # 3. 후처리 및 구조화
    # structured_data = extract_structured_data(results)

    # 결과 출력
    # print("📋 구조화된 병원 영수증 정보:")
    # for key, value in structured_data.items():
    #     print(f"{key}: {value}")

    output_folder = PATH_OUTPUTS / image_path.stem
    ocr_json = output_folder / f'{image_path.stem}_res.json'
    process_ppstructure(ocr_json, output_folder)

