import re

def extract_structured_data(ocr_results):
    structured = {
        "병원명": None,
        "날짜": None,
        "총금액": None,
        "진료내역": []
    }

    for line in ocr_results[0]:
        text = line[1][0]

        # 병원명 추출 (예: '서울의료원')
        if not structured["병원명"] and "의료원" in text or "병원" in text:
            structured["병원명"] = text

        # 날짜 추출
        date_match = re.search(r'\d{4}[./-]\d{1,2}[./-]\d{1,2}', text)
        if date_match:
            structured["날짜"] = date_match.group()

        # 총금액 추출
        if "합계" in text or "총액" in text:
            amount_match = re.search(r'\d{1,3}(,\d{3})*(원)?', text)
            if amount_match:
                structured["총금액"] = amount_match.group()

        # 진료내역 추출
        if any(keyword in text for keyword in ["진료", "처방", "약제", "검사"]):
            structured["진료내역"].append(text)

    return structured
