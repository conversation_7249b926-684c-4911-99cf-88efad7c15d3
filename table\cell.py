from pathlib import Path
import pandas as pd
from table.cell import get_cell
from utils import load_ppstructure_json, convert_type, is_ocr_mostly_inside_cell
from utils import draw_multiple_bbox_sets, draw_multiple_line_sets
from config import PATH_OUTPUTS, PATH_DATA


def get_cell(cells, cid) -> dict:
        for cell in cells:
            if cid == cell['cid']:
                return cell
        return {}


def cell_info(df, cell_id) -> dict:
    def get_row(id):
        if pd.isna(id):
            return {}
        return df[df['cell_id'] == id].iloc[0]
    row = get_row(cell_id)
    left, right, above, below = row[['left_id', 'right_id', 'above_id', 'below_id']]
    row_left = get_row(left)
    row_right = get_row(right)
    row_above = get_row(above)
    row_below = get_row(below)
    
    row['left_id'] = row_left.get('texts')
    row['right_id'] = row_right.get('texts')
    row['above_id'] = row_above.get('texts')
    row['below_id'] = row_below.get('texts')
    row_dict = row.to_list()

    return row_dict


def compute_guide_lines(table_bbox, cells, x_tol=5, y_tol=5, freq_threshold=4):
    # Compute guide lines based on table bbox boundaries and cells' bbox x0 and y0 values.
    table_x0, table_y0, table_x1, table_y1 = table_bbox
    x_candidates = [table_x0, table_x1]
    y_candidates = [table_y0, table_y1]

    for cell in cells:
        bbox = cell['bbox']
        x_candidates.append(bbox[0])
        y_candidates.append(bbox[1])

    def _filter_noisy_lines(lines, tol, freq_threshold):
        # Sort lines and group coordinates within tolerance.
        lines.sort()
        groups = []
        group = [lines[0]]
        for line in lines[1:]:
            if abs(line - group[-1]) <= tol:
                group.append(line)
            else:
                groups.append(group)
                group = [line]
        groups.append(group)
        
        # Compute the average of each group and filter out groups with low frequency.
        filtered = []
        for group in groups:
            if len(group) >= freq_threshold:
                avg_line = sum(group) / len(group)
                filtered.append(int(avg_line))
        return filtered

    lines_x = _filter_noisy_lines(x_candidates, x_tol, freq_threshold)
    lines_y = _filter_noisy_lines(y_candidates, y_tol, freq_threshold)
    return lines_x, lines_y


def snap_cells_to_guide_lines(cells, x_lines, y_lines, x_tol=5, y_tol=5):
    def snap_value(value, guide_lines, tol):
        for g in guide_lines:
            if abs(value - g) <= tol:
                return g
        return value

    for cell in cells:
        bbox = cell['bbox']
        x0, y0, x1, y1 = bbox
        new_x0 = snap_value(x0, x_lines, x_tol)
        new_y0 = snap_value(y0, y_lines, y_tol)
        new_x1 = snap_value(x1, x_lines[1:], x_tol)
        new_y1 = snap_value(y1, y_lines[1:], y_tol)
        cell['bbox'] = [new_x0, new_y0, new_x1, new_y1]

    # cell_bboxes = [cell['bbox'] for cell in cells]
    # draw_multiple_bbox_sets(PATH_DATA / file, [cell_bboxes], PATH_DATA / 'snapped_cells.png')
    return cells


# cells중 gap이 있는 부분을 채우기 위한 전처리
def fill_gaps(cells, table_bbox, y_tol=2, x_tol=2):
    """
    각 cell 별로 주변 셀과의 간격이 있으면 cell의 bbox를 확장하여 gap을 채웁니다.
    각 cell의 상하좌우를 table boundary 또는 인접한 셀의 경계까지 확장합니다.
    """
    table_x0, table_y0, table_x1, table_y1 = table_bbox
    for cell in cells:
        x0, y0, x1, y1 = cell['bbox']

        # Expand left: extend x0 to the right boundary of neighbors on the left.
        new_x0 = table_x0
        for other in cells:
            if other is cell:
                continue
            ox0, oy0, ox1, oy1 = other['bbox']
            # If other is completely left of current and overlaps vertically.
            if ox1-x_tol <= x0 and not (oy1+y_tol <= y0 or oy0-y_tol >= y1):
                new_x0 = max(new_x0, ox1)

        # Expand right: shrink x1 to the left boundary of neighbors on the right.
        new_x1 = table_x1
        for other in cells:
            if other is cell:
                continue
            ox0, oy0, ox1, oy1 = other['bbox']
            # If other is completely right of current and overlaps vertically.
            if ox0+x_tol >= x1 and not (oy1+y_tol <= y0 or oy0-y_tol >= y1):
                new_x1 = min(new_x1, ox0)

        # Expand up: extend y0 to the bottom boundary of neighbors above.
        new_y0 = table_y0
        for other in cells:
            if other is cell:
                continue
            ox0, oy0, ox1, oy1 = other['bbox']
            # If other is completely above current and overlaps horizontally.
            if oy1-y_tol <= y0 and not (ox1+x_tol <= x0 or ox0-x_tol >= x1):
                new_y0 = max(new_y0, oy1)

        # Expand down: shrink y1 to the top boundary of neighbors below.
        new_y1 = table_y1
        for other in cells:
            if other is cell:
                continue
            ox0, oy0, ox1, oy1 = other['bbox']
            # If other is completely below current and overlaps horizontally.
            if oy0+y_tol >= y1 and not (ox1+x_tol <= x0 or ox0-x_tol >= x1):
                new_y1 = min(new_y1, oy0)

        cell['bbox'] = [new_x0, new_y0, new_x1, new_y1]
    
    print('# of cells:', len(cells))
    cell_bboxes = [cell['bbox'] for cell in cells]
    from meta_table2 import file
    draw_multiple_bbox_sets(PATH_DATA / file, [cell_bboxes], PATH_OUTPUTS / file.stem / 'debug_filled_gaps.png')
    return cells


def update_cells_with_neighbors(cells, max_gap=3, tol_ratio=0.8):
    """
    각 cell의 bbox 정보를 바탕으로 좌, 우, 위, 아래 이웃 cell을 찾아서
    각 cell에 이웃 정보(이웃 cell의 index와 text)를 추가합니다.
    
    이웃 판단 조건:
      - 좌우: 한 cell의 좌측(border)와 다른 cell의 우측(border)이 max_gap 이내,
             그리고 두 cell의 수직 영역이 충분히 겹치거나(겹치는 길이가 두 cell 높이의 최소값의 tol_ratio 이상)
      - 상하: 한 cell의 상단(border)와 다른 cell의 하단(border)이 max_gap 이내,
             그리고 두 cell의 수평 영역이 충분히 겹치거나(겹치는 길이가 두 cell 폭의 최소값의 tol_ratio 이상)
             
    span하는 cell의 경우, 한 방향에 대해 여러 이웃을 포함할 수 있습니다.
    
    인수:
      cells: 각 cell이 {'cid': int, 'bbox': [x0, y0, x1, y1], ... } 형태인 리스트
      cell_text: 각 cell의 텍스트를 담은 리스트 (인덱스가 cell id와 일치)
      max_gap: 경계 간 최대 허용 gap (픽셀 단위)
      tol_ratio: 겹침 판단 비율 (0~1 사이)
      
    반환:
      cells: 각 cell dict에 'left_idx', 'left_text', 'right_idx', 'right_text',
             'above_idx', 'above_text', 'below_idx', 'below_text' 키가 추가됨.
    """
    def vertical_overlap(bbox1, bbox2):
        y0 = max(bbox1[1], bbox2[1])
        y1 = min(bbox1[3], bbox2[3])
        return max(0, y1 - y0)
    
    def horizontal_overlap(bbox1, bbox2):
        x0 = max(bbox1[0], bbox2[0])
        x1 = min(bbox1[2], bbox2[2])
        return max(0, x1 - x0)
    
    for cell in cells:
        bbox_i = cell['bbox']
        x0_i, y0_i, x1_i, y1_i = bbox_i
        height_i = y1_i - y0_i
        width_i = x1_i - x0_i
        
        left_neighbors = []
        right_neighbors = []
        above_neighbors = []
        below_neighbors = []
        
        for other in cells:
            if cell is other:
                continue
            bbox_j = other['bbox']
            x0_j, y0_j, x1_j, y1_j = bbox_j
            height_j = y1_j - y0_j
            width_j = x1_j - x0_j
            cell_text = ''.join(other['tokens'])

            # Check left neighbor: other cell's right side nearly touches cell_i's left side
            if abs(x0_i - x1_j) <= max_gap:
                overlap = vertical_overlap(bbox_i, bbox_j)
                if min(height_i, height_j) > 0 and (overlap / min(height_i, height_j) >= tol_ratio):
                    left_neighbors.append((other['cid'], cell_text))
                    
            # Check right neighbor: other cell's left side nearly touches cell_i's right side
            if abs(x0_j - x1_i) <= max_gap:
                overlap = vertical_overlap(bbox_i, bbox_j)
                if min(height_i, height_j) > 0 and (overlap / min(height_i, height_j) >= tol_ratio):
                    right_neighbors.append((other['cid'], cell_text))
                    
            # Check above neighbor: other cell's bottom nearly touches cell_i's top
            if abs(y0_i - y1_j) <= max_gap:
                overlap = horizontal_overlap(bbox_i, bbox_j)
                if min(width_i, width_j) > 0 and (overlap / min(width_i, width_j) >= tol_ratio):
                    above_neighbors.append((other['cid'], cell_text))
                    
            # Check below neighbor: other cell's top nearly touches cell_i's bottom
            if abs(y0_j - y1_i) <= max_gap:
                overlap = horizontal_overlap(bbox_i, bbox_j)
                if min(width_i, width_j) > 0 and (overlap / min(width_i, width_j) >= tol_ratio):
                    below_neighbors.append((other['cid'], cell_text))
                    
        cell['left_idx'] = [n[0] for n in left_neighbors]
        cell['right_idx'] = [n[0] for n in right_neighbors]
        cell['above_idx'] = [n[0] for n in above_neighbors]
        cell['below_idx'] = [n[0] for n in below_neighbors]
        cell['left_text'] = [n[1] for n in left_neighbors]
        cell['right_text'] = [n[1] for n in right_neighbors]
        cell['above_text'] = [n[1] for n in above_neighbors]
        cell['below_text'] = [n[1] for n in below_neighbors]
    
    return cells


def cluster_row_cells(cells, y_tol=5) -> list:
    """y0를 기준으로 row 그룹 만들기. 각 그룹은 [cell_ids] 리스트"""

    row_groups = []  # 각 그룹은 [cell_ids] 리스트
    
    for cell in cells:
        cid, bbox = cell['cid'], cell['bbox']
        
        # 기존 그룹 중 y_tol 범위 내에 있는지 확인
        assigned = False
        for group_idx, group in enumerate(row_groups):
            # 그룹의 대표 y 좌표 계산
            y0s = [cells[cid]['bbox'][1] for cid in group]
            group_y0 = sum(y0s) / len(y0s)

            if abs(group_y0 - bbox[1]) <= y_tol:
                row_groups[group_idx].append(cid)
                assigned = True
                break
        
        if not assigned:
            row_groups.append([cid])

    cell_bboxes = [[get_cell(cells, cid)['bbox'] for cid in group] for group in row_groups]
    from meta_table2 import file
    draw_multiple_bbox_sets(PATH_DATA / file, cell_bboxes)
    return row_groups