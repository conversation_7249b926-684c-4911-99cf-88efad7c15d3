# 공통으로 사용될 전역변수 설정
import os
from pathlib import Path


DEBUG = False


# 최상위 PATH
PATH_CWD = Path(__file__).parent
PATH_OUTPUTS = PATH_CWD / 'outputs'
PATH_OUTPUTS.mkdir(parents=True, exist_ok=True)
PATH_DATA = PATH_CWD / 'dataset'
PATH_DATA.mkdir(parents=True, exist_ok=True)
PATH_TEMP = PATH_CWD / 'temp'
PATH_TEMP.mkdir(parents=True, exist_ok=True)


# 프로젝트 루트로 HOME 경로 설정
os.environ["HOME"] = str(PATH_CWD)

API_KEY = "**********************************************************"  # 예시 API 키, 실제 사용 시 변경 필요
