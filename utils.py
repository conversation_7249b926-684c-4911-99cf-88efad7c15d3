from pathlib import Path
import json
from config import PATH_OUTPUTS


def flatten_entity_labels(entity_json):
    """
    중첩된 entity JSON에서 모든 문자열(label)만 추출해 하나의 집합(set)으로 반환.
    """
    labels = set()
    def recurse(obj):
        if isinstance(obj, dict):
            for v in obj.values(): recurse(v)
        elif isinstance(obj, list):
            for x in obj:
                if isinstance(x, str): labels.add(x)
                else: recurse(x)
    recurse(entity_json)
    return labels


def convert_type(group:list, cast) -> list:
    """중첩된 list들에 type cast를 적용"""
    if isinstance(group, list) == False:
        return cast(group)
    return [convert_type(elm, cast) for elm in group]


def load_ppstructure_json(ppjson_path):
    """PPStructure JSON 파일 로드"""
    return json.loads(Path(ppjson_path).read_text(encoding='utf-8'))


def iou(box1:list, box2:list) -> int:
    """intersection over union"""

    x0 = max(box1[0], box2[0])
    y0 = max(box1[1], box2[1])
    x1 = min(box1[2], box2[2])
    y1 = min(box1[3], box2[3])

    inter_area = max(0, x1 - x0) * max(0, y1 - y0)
    box1_area = (box1[2] - box1[0]) * (box1[3] - box1[1])
    box2_area = (box2[2] - box2[0]) * (box2[3] - box2[1])

    union = box1_area + box2_area
    return inter_area / union if union > 0 else 0

def inclusion_ratio(cell_bbox: list, ocr_bbox: list) -> float:
    x0 = max(cell_bbox[0], ocr_bbox[0])
    y0 = max(cell_bbox[1], ocr_bbox[1])
    x1 = min(cell_bbox[2], ocr_bbox[2])
    y1 = min(cell_bbox[3], ocr_bbox[3])

    inter_area = max(0, x1 - x0) * max(0, y1 - y0)
    ocr_area = (ocr_bbox[2] - ocr_bbox[0]) * (ocr_bbox[3] - ocr_bbox[1])

    return inter_area / ocr_area if ocr_area > 0 else 0


def is_overlap_enough(cell_bbox:list, ocr_bbox:list, threshold=0.8) -> bool:
    return inclusion_ratio(cell_bbox, ocr_bbox) >= threshold


def is_dimensionally_included(cell_bbox: list, ocr_bbox: list, threshold: float = 0.8) -> bool:
    """
    OCR 박스의 가로/세로 길이가 셀 박스 안에 얼마나 포함되는지를 기준으로 판단.
    threshold 이상이면 포함된 것으로 간주.
    """
    # 셀과 OCR의 좌표
    cx0, cy0, cx1, cy1 = cell_bbox
    ox0, oy0, ox1, oy1 = ocr_bbox

    # 교차 영역
    ix0 = max(cx0, ox0)
    iy0 = max(cy0, oy0)
    ix1 = min(cx1, ox1)
    iy1 = min(cy1, oy1)

    # OCR 박스의 너비/높이
    ocr_width = ox1 - ox0
    ocr_height = oy1 - oy0

    # 교차 영역의 너비/높이
    inter_width = max(0, ix1 - ix0)
    inter_height = max(0, iy1 - iy0)

    # 각각의 비율이 threshold 이상인지 확인
    width_ratio = inter_width / ocr_width if ocr_width > 0 else 0
    height_ratio = inter_height / ocr_height if ocr_height > 0 else 0

    return width_ratio >= threshold or height_ratio >= threshold


from PIL import Image, ImageDraw

def draw_multiple_bbox_sets(image_path, bbox_sets, output_path=PATH_OUTPUTS / "output_image.png"):
    """
    이미지에 여러 개의 bounding box 리스트를 각각 다른 색상으로 그려 저장합니다.

    Parameters:
    - image_path (str): 원본 이미지 경로
    - bbox_sets (list of list of tuples): 각 리스트는 (x0, y0, x1, y1) 형식의 bbox 튜플을 포함
    - output_path (str): 결과 이미지 저장 경로
    """
    # 이미지 열기
    image = Image.open(image_path).convert("RGB")
    draw = ImageDraw.Draw(image)

    # 각 bbox 세트에 대해 고유한 색상 생성
    rgb_colors = [
                    (255, 0, 0),     # Red
                    (0, 255, 0),     # Green
                    (0, 0, 255),     # Blue
                    (0, 255, 255),   # Cyan
                    (255, 0, 255),   # Magenta
                    (255, 255, 0),   # Yellow
                    (128, 0, 128),   # Purple
                    (255, 165, 0),   # Orange
                    (0, 128, 128),   # Teal
                    (128, 128, 0),   # Olive
                ]


    # 각 bbox 세트를 해당 색상으로 그림
    for set_index, bbox_list in enumerate(bbox_sets):
        color = rgb_colors[set_index % len(rgb_colors)]  # 색상 순환

        for bbox in bbox_list:
            draw.rectangle(bbox, outline=color, width=1)

    # 결과 이미지 저장
    image.save(output_path)
    print(f"Image with bounding boxes saved to {output_path}")


def draw_multiple_line_sets(image_path, line_sets, output_path=PATH_OUTPUTS / "output_image.png"):
    """
    이미지에 여러 개의 bounding box 리스트를 각각 다른 색상으로 그려 저장합니다.

    Parameters:
    - image_path (str): 원본 이미지 경로
    - bbox_sets (list of list of tuples): 각 리스트는 (x0, y0, x1, y1) 형식의 bbox 튜플을 포함
    - output_path (str): 결과 이미지 저장 경로
    """
    # 이미지 열기
    image = Image.open(image_path).convert("RGB")
    draw = ImageDraw.Draw(image)

    # 각 bbox 세트에 대해 고유한 색상 생성
    rgb_colors = [
                    (255, 0, 0),     # Red
                    (0, 255, 0),     # Green
                    (0, 0, 255),     # Blue
                    (0, 255, 255),   # Cyan
                    (255, 0, 255),   # Magenta
                    (255, 255, 0),   # Yellow
                    (128, 0, 128),   # Purple
                    (255, 165, 0),   # Orange
                    (0, 128, 128),   # Teal
                    (128, 128, 0),   # Olive
                ]


    # 각 bbox 세트를 해당 색상으로 그림
    for set_index, line_list in enumerate(line_sets):
        color = rgb_colors[set_index % len(rgb_colors)]  # 색상 순환

        for pnt in line_list:
            draw.line(pnt, fill=color, width=1)

    # 결과 이미지 저장
    image.save(output_path)
    print(f"Image with bounding boxes saved to {output_path}")

