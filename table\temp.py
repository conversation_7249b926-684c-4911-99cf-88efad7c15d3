from pathlib import Path
from utils import load_ppstructure_json, convert_type, is_overlap_enough, is_ocr_mostly_inside_cell
from utils import draw_multiple_bbox_sets, draw_multiple_line_sets
from table.cell import compute_guide_lines, snap_cells_to_guide_lines, fill_gaps, assign_tokens_to_cells, update_cells_with_neighbors
from table.cell import get_cell
from config import PATH_OUTPUTS, PATH_DATA


def compute_cell_row_span(cells, cell_row, sorted_row_groups, y_tol=5):
    """셀의 y 범위를 기준으로 row span 계산"""
    # 각 row의 y 범위 계산
    row_y_ranges = []
    for group in sorted_row_groups:
        ys = [cells[cid][1] for cid in group] + [cells[cid][3] for cid in group]
        row_min_y = min(ys)
        row_max_y = max(ys)
        row_y_ranges.append((row_min_y, row_max_y))

    # 셀별 row span 계산
    cell_row_span = {}
    for cid, (x0, y0, x1, y1) in enumerate(cells):
        cell_min_y = min(y0, y1)
        cell_max_y = max(y0, y1)
        span = 0
        for row_idx, (row_min_y, row_max_y) in enumerate(row_y_ranges):
            if cell_max_y >= row_min_y - y_tol and cell_min_y <= row_max_y + y_tol:
                span += 1
        cell_row_span[cid] = span if span > 0 else 1  # 최소 1

    return cell_row_span


def sort_cells_row(cid_rows: list, cells: list) -> list:
    """각 row와 row 안의 cell들을 정렬합니다.
    - Row 내부의 cell은 x0 기준으로 정렬합니다.
    - Row 자체는 첫번째 cell의 y0 기준으로 위에서 아래로 정렬합니다.
    """
    sorted_rows = []
    for row in cid_rows:
        sorted_row = sorted(row, key=lambda cid: get_cell(cells, cid)['bbox'][0])
        sorted_rows.append(sorted_row)
    
    sorted_rows.sort(key=lambda row: get_cell(cells, row[0])['bbox'][1])
    
    return sorted_rows





def cluster_cells_to_grid(cells, y_tol=5, x_tol=5):
    """셀을 그리드(row/col)로 클러스터링하고 좌표 순서대로 정렬"""
    # 1단계: y_center, height 기준으로 row 그룹 만들기
    row_groups = []  # 각 그룹은 [cell_ids] 리스트
    
    for cid, (cx0, cy0, cx1, cy1) in enumerate(cells):
        y_center = (cy0 + cy1) / 2
        cell_height = abs(cy0 - cy1)
        
        # 기존 그룹 중 y_tol 범위 내에 있는지 확인
        assigned = False
        for group_idx, group in enumerate(row_groups):
            # 그룹의 대표 y 좌표 계산
            centers_y = [(cells[c][1] + cells[c][3])/2 for c in group]
            group_y = sum(centers_y) / len(centers_y)
            # 그룹의 height 계산
            heights = [cells[c][3] - cells[c][1] for c in group]
            group_height = sum(heights) / len(heights)

            if abs(group_y - y_center) <= y_tol and abs(group_height - cell_height) <= y_tol:
                row_groups[group_idx].append(cid)
                assigned = True
                break
        
        if not assigned:
            row_groups.append([cid])
    
    # 2단계: row 그룹들을 y 좌표 순서대로 정렬 (위에서 아래로: 작은 y부터)
    row_groups_with_y = []
    for group in row_groups:
        min_y = min(cells[c][1] for c in group)
        row_groups_with_y.append((min_y, group))
    
    # y 좌표가 작을수록 위쪽이므로 오름차순 정렬
    row_groups_with_y.sort(key=lambda elm: elm[0])
    sorted_row_groups = [group for _, group in row_groups_with_y]

    # 3단계: group 내에서 x좌표를 기준으로 오름차순 정렬
    for idx, group in enumerate(sorted_row_groups):
        row_groups_with_x = [(cells[c][0], c) for c in group]
        row_groups_with_x.sort(key=lambda elm: elm[0])
        sorted_row_groups[idx] = [group for _, group in row_groups_with_x]
            
    
    # 4단계: 정렬된 순서로 row / col 번호 할당 (위에서부터 0, 1, 2, ...)
    # spanning 정보에 따라 row / col 번호 조정 필요
    # 4단계 개선: 셀 크기(span) 고려하여 col 인덱스 할당
    cell_row = {}
    cell_col = {}
    cell_col_span = {}

    for row_idx, group in enumerate(sorted_row_groups):
        # 1. 해당 row의 셀들의 x 좌표 수집
        x_positions = []
        for cid in group:
            x0, _, x1, _ = cells[cid]
            x_positions.append((x0, x1))
        
        # 2. x 좌표 기준으로 col 영역 정의
        x_positions.sort()
        col_boundaries = []
        for x0, x1 in x_positions:
            if not col_boundaries or x0 > col_boundaries[-1] + x_tol:
                col_boundaries.append(x0)
        
        # 3. 셀별로 col index 및 span 계산
        for cid in group:
            x0, _, x1, _ = cells[cid]
            start_col = None
            end_col = None
            for col_idx, col_x in enumerate(col_boundaries):
                if start_col is None and x0 <= col_x + x_tol:
                    start_col = col_idx
                if x1 >= col_x - x_tol:
                    end_col = col_idx
            if start_col is not None and end_col is not None:
                cell_row[cid] = row_idx
                cell_col[cid] = start_col
                cell_col_span[cid] = end_col - start_col + 1

    cell_row_span = compute_cell_row_span(cells, cell_row, sorted_row_groups, y_tol)
    
    return cell_row, cell_col, cell_col_span, cell_row_span

# def transpose_rows_to_columns(filled_rows):
#     max_len = max(len(row) for row in filled_rows)
#     columns = [[] for _ in range(max_len)]
#     for row in filled_rows:
#         for i, cell in enumerate(row):
#             columns[i].append(cell)
#     return columns


# def transpose_columns_to_rows(filled_columns):
#     max_len = max(len(col) for col in filled_columns)
#     rows = [[] for _ in range(max_len)]
#     for col in filled_columns:
#         for i, cell in enumerate(col):
#             rows[i].append(cell)
#     return rows

# def save_tokens_csv(out_dir, tidx, texts, boxes, token_cells, scores=None, angles=None):
#     """토큰별 정보를 CSV로 저장 (검증용)"""
#     out_dir.mkdir(parents=True, exist_ok=True)
#     csv_path = out_dir / f"table_{tidx}_tokens.csv"
    
#     with csv_path.open('w', newline='', encoding='utf-8-sig') as f:
#         w = csv.writer(f)
#         w.writerow(['token_id', 'text', 'bbox', 'assigned_cell_id', 'confidence', 'angle'])
        
#         for tid, (text, box, cell_id) in enumerate(zip(texts, boxes, token_cells)):
#             x0, y0, x1, y1 = box
#             score = scores[tid] if scores and tid < len(scores) else None
#             angle = angles[tid] if angles and tid < len(angles) else None
#             w.writerow([tid, text, f"[{round(x0)},{round(y0)},{round(x1)},{round(y1)}]", cell_id, score, angle])
    
#     print(f"Saved tokens: {csv_path}")
#     return csv_path


def merge_cells(filled_rows, axis='y', threshold=0.8):
    """겹치는 셀들을 병합하여 multi-level 구조 복원"""
    merged_cells = []

    for row in filled_rows:
        for cell in row:
            if cell['cid'] == 'missing':
                continue  # 'missing' 셀은 병합 대상에서 제외하거나 별도로 처리

            overlapping = [c for c in merged_cells if is_overlapping(c['bbox'], cell['bbox'], axis=axis, threshold=threshold)]
            if overlapping:
                for target in overlapping:
                    target['bbox'] = merge_bbox(target['bbox'], cell['bbox'])
                    target.setdefault('merged_cids', []).append(cell['cid'])
            else:
                merged_cells.append({**cell, 'merged_cids': [cell['cid']]})

    return merged_cells


def is_overlapping(bbox1, bbox2, axis='y', threshold=0.8):
    """
    bbox1과 bbox2가 지정된 축(axis)에서 일정 비율 이상 겹치는지 확인.
    axis: 'x' 또는 'y'
    threshold: 겹침 비율 기준 (0~1)
    """
    if axis == 'x':
        start1, end1 = bbox1[0], bbox1[2]
        start2, end2 = bbox2[0], bbox2[2]
    else:  # 'y'
        start1, end1 = bbox1[1], bbox1[3]
        start2, end2 = bbox2[1], bbox2[3]

    overlap = max(0, min(end1, end2) - max(start1, start2))
    length1 = end1 - start1
    length2 = end2 - start2

    return (overlap / length1 >= threshold) or (overlap / length2 >= threshold)


def merge_bbox(bbox1, bbox2):
    """
    두 bbox를 하나의 bbox로 병합.
    """
    x0 = min(bbox1[0], bbox2[0])
    y0 = min(bbox1[1], bbox2[1])
    x1 = max(bbox1[2], bbox2[2])
    y1 = max(bbox1[3], bbox2[3])
    return [x0, y0, x1, y1]

