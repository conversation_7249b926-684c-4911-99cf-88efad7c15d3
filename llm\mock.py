import re
from typing import Dict, Any, List

_WS = re.compile(r"\s+")

def _norm(s: str) -> str:
    return _WS.sub("", s or "")

def _texts(side_blob: Dict[str, Any]) -> List[str]:
    # context["neighbors"][side] 구조에서 texts/detailed 둘 다 방어적으로 사용
    t = (side_blob or {}).get("texts") or []
    if not t and (side_blob or {}).get("detailed"):
        t = [d.get("text", "") for d in side_blob["detailed"]]
    return t

def _ids(side_blob: Dict[str, Any]) -> List[int]:
    return [(side_blob or {}).get("cids") or []]

def _flat_ids(side_blob: Dict[str, Any]) -> List[int]:
    return list((side_blob or {}).get("cids") or [])

def _iou_1d(a1, a2, b1, b2):
    # 1D IoU (행/열 정렬 판단용)
    inter = max(0, min(a2, b2) - max(a1, b1))
    union = (a2 - a1) + (b2 - b1) - inter
    return inter / union if union > 0 else 0.0

def _same_row(b1, b2, tol=0.30):
    # y-축(세로) overlap로 같은 행 여부
    return _iou_1d(b1[1], b1[3], b2[1], b2[3]) >= tol

def _same_col(b1, b2, tol=0.30):
    # x-축(가로) overlap로 같은 열 여부
    return _iou_1d(b1[0], b1[2], b2[0], b2[2]) >= tol

def mock_engine(context: dict) -> dict:
    """
    - 공백 제거/간단 치환으로 corrected_text 생성
    - 상/하/좌/우 모두 고려하여 수직/수평 분절 병합 후보 제안
    """
    cid = context["cid"]
    bbox = context["bbox"]
    me_txt = _norm(context.get("text", ""))

    # 1) 텍스트 보정(간단): 전각/반각 혼용, 공백/군더더기 제거 예시
    corrected = me_txt.replace("진 단", "진단").replace("계 산", "계산")

    tgt = set()
    reason_parts = []

    # 2) 수직 병합 후보(above/below)
    for side in ("above", "below"):
        blob = context["neighbors"].get(side, {})
        for d in blob.get("detailed", []):
            n_txt = _norm(d.get("text", ""))
            n_bbox = d.get("bbox", [0,0,0,0])
            # 조건: (텍스트가 같거나 매우 짧은 조각) & 같은 열
            if (n_txt and (n_txt == corrected or (len(n_txt) <= 2 and corrected.startswith(n_txt)))) and _same_col(bbox, n_bbox):
                tgt.add(d["cid"])
                reason_parts.append(f"{side} split")

    # 3) 수평 병합 후보(left/right)
    for side in ("left", "right"):
        blob = context["neighbors"].get(side, {})
        for d in blob.get("detailed", []):
            n_txt = _norm(d.get("text", ""))
            n_bbox = d.get("bbox", [0,0,0,0])
            # 조건: (텍스트가 같거나 매우 짧은 조각) & 같은 행
            if (n_txt and (n_txt == corrected or (len(n_txt) <= 2 and corrected.endswith(n_txt)))) and _same_row(bbox, n_bbox):
                tgt.add(d["cid"])
                reason_parts.append(f"{side} split")

    merge = None
    if tgt:
        merge = {
            "target_cids": sorted(tgt),
            "reason": " / ".join(sorted(set(reason_parts))),
            "axis": "both" if any("left" in r or "right" in r for r in reason_parts) and any("above" in r or "below" in r for r in reason_parts)
                    else ("x" if any("left" in r or "right" in r for r in reason_parts) else "y")
        }

    return {"cid": cid, "corrected_text": corrected, "merge": merge}
