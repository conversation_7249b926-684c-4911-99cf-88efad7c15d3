import json
import csv


def make_entity_table_from_ocr(json_file, output_file, y_margin=10):
    # Load the JSON content from the file
    with open(json_file, 'r', encoding='utf-8') as f:
        ocr_data = json.load(f)

    # Extract parallel lists
    texts = ocr_data.get('rec_texts', [])
    bboxes = ocr_data.get('rec_boxes', [])
    angles = ocr_data.get('textline_orientation_angles', [])

    # Validate lengths
    if not (len(texts) == len(bboxes) == len(angles)):
        raise ValueError("Lengths of rec_texts, rec_boxes, and textline_orientation_angles do not match.")

    # Build entities
    entity_list = []
    for text, bbox, angle in zip(texts, bboxes, angles):
        entity_list.append({
            'text': text,
            'bbox': bbox,        # [x0, y0, x1, y1]
            'angle': angle,
        })

    # Cluster by Y coordinate into rows
    row_centers = []  # list of representative y for each row
    for e in entity_list:
        _, y0, _, y1 = e['bbox']
        y_center = (y0 + y1) / 2
        # find existing row within margin
        for idx, rc in enumerate(row_centers):
            if abs(rc - y_center) <= y_margin:
                e['row'] = idx
                break
        else:
            # new row
            e['row'] = len(row_centers)
            row_centers.append(y_center)

    # Sort: first by row, then by x coordinate
    entity_list.sort(key=lambda e: (e['row'], e['bbox'][0]))

    # Write to CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        fieldnames = ['row', 'text', 'bbox', 'angle']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for e in entity_list:
            writer.writerow({
                'row': e['row'],
                'text': e['text'],
                'bbox': json.dumps(e['bbox'], ensure_ascii=False),
                'angle': e['angle'],
            })

    print(f"Saved {len(entity_list)} entities grouped into {len(row_centers)} rows to {output_file}.")


import statistics

def group_ocr_neighbors_directional(ocr_json_path, output_json_path):
    """
    For each OCR token, find its nearest neighbors in four directions
    (left, right, above, below) based on bbox geometry and cell overlap,
    filtered by estimated row height to avoid distant cells.
    """
    # Load OCR results
    with open(ocr_json_path, 'r', encoding='utf-8') as f:
        ocr = json.load(f)

    texts = ocr.get('rec_texts', [])
    boxes = ocr.get('rec_boxes', [])  # list of [x0, y0, x1, y1]
    n = len(texts)

    # Compute median row height for vertical filtering
    heights = [(y1 - y0) for x0, y0, x1, y1 in boxes]
    row_height = statistics.median(heights) if heights else 0

    grouped = []
    for i in range(n):
        text_i = texts[i]
        x0_i, y0_i, x1_i, y1_i = boxes[i]
        height_i = y1_i - y0_i
        width_i  = x1_i - x0_i

        neighbors = {'left': None, 'right': None, 'above': None, 'below': None}
        best_left = best_right = best_above = best_below = float('inf')

        for j in range(n):
            if i == j:
                continue
            text_j = texts[j]
            x0_j, y0_j, x1_j, y1_j = boxes[j]
            height_j = y1_j - y0_j
            width_j  = x1_j - x0_j

            # Vertical overlap check for left/right
            y_overlap = min(y1_i, y1_j) - max(y0_i, y0_j)
            if y_overlap >= min(height_i, height_j) * 0.5:
                # Left neighbor
                if x1_j <= x0_i:
                    dist = x0_i - x1_j
                    if dist < best_left:
                        best_left = dist
                        neighbors['left'] = {'text': text_j, 'bbox': boxes[j]}
                # Right neighbor
                if x0_j >= x1_i:
                    dist = x0_j - x1_i
                    if dist < best_right:
                        best_right = dist
                        neighbors['right'] = {'text': text_j, 'bbox': boxes[j]}

            # Horizontal overlap check for above/below
            x_overlap = min(x1_i, x1_j) - max(x0_i, x0_j)
            if x_overlap >= min(width_i, width_j) * 0.5:
                # Above neighbor
                if y1_j <= y0_i:
                    dist = y0_i - y1_j
                    if dist < best_above and dist <= row_height * 1.2:
                        best_above = dist
                        neighbors['above'] = {'text': text_j, 'bbox': boxes[j]}
                # Below neighbor
                if y0_j >= y1_i:
                    dist = y0_j - y1_i
                    if dist < best_below and dist <= row_height * 1.2:
                        best_below = dist
                        neighbors['below'] = {'text': text_j, 'bbox': boxes[j]}

        grouped.append({'text': text_i, 'bbox': boxes[i], 'neighbors': neighbors})

    # Save to JSON
    with open(output_json_path, 'w', encoding='utf-8') as f:
        json.dump(grouped, f, ensure_ascii=False, indent=2)

    print(f"Directional neighbors grouped for {n} tokens --> {output_json_path}")


import csv
from config import PATH_OUTPUTS

def ocr_neighbors_json_to_csv(json_input_path, csv_output_path):
    """
    Convert an OCR neighbors JSON file to CSV.
    CSV columns: text, bbox, left_text, left_bbox, right_text, right_bbox,
                 above_text, above_bbox, below_text, below_bbox
    """
    # Load grouped OCR JSON
    with open(json_input_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Define CSV fieldnames
    fieldnames = [
        'text', 'bbox',
        'left_text', 'left_bbox',
        'right_text', 'right_bbox',
        'above_text', 'above_bbox',
        'below_text', 'below_bbox'
    ]
    
    # Write to CSV
    with open(csv_output_path, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for rec in data:
            row = {
                'text': rec.get('text', ''),
                'bbox': json.dumps(rec.get('bbox', []), ensure_ascii=False)
            }
            neighbors = rec.get('neighbors', {})
            for direction in ('left', 'right', 'above', 'below'):
                nb = neighbors.get(direction)
                if nb:
                    row[f'{direction}_text'] = nb.get('text', '')
                    row[f'{direction}_bbox'] = json.dumps(nb.get('bbox', []), ensure_ascii=False)
                else:
                    row[f'{direction}_text'] = ''
                    row[f'{direction}_bbox'] = ''
            writer.writerow(row)
    
    print(f"Saved OCR neighbors CSV: {csv_output_path}")


if __name__ == '__main__':
    from config import PATH_DATA, PATH_OUTPUTS
    filename = '의료비영수증5'
    json_file = PATH_OUTPUTS / f'{filename}_res.json'
    output_file = PATH_OUTPUTS / f'{filename}_res_rowsorted.csv'
    make_entity_table_from_ocr(json_file, output_file, y_margin=12)

    # 예시 사용
    ocr_json = PATH_OUTPUTS / '의료비영수증5_res.json'
    entity_json = PATH_DATA / 'entity.json'
    output = PATH_OUTPUTS / 'ocr_grouped_neighbors.json'
    group_ocr_neighbors_directional(ocr_json, output)
    csv_output  = PATH_OUTPUTS / 'ocr_neighbors.csv'
    ocr_neighbors_json_to_csv(output, csv_output)

