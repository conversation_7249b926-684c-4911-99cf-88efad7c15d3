
import os
import cv2
import json
from paddleocr import PPStructure
import numpy as np
# 전처리를 위해 numpy 추가

# ─── 기본 디렉토리 설정 ──────────────────────────

BASE_DIR = r"D:\Receipt_Hospital\PaddleOCR\ppstructure\inference"


# ─── 모델 디렉토리 (한국어 Table+Recognition) ────────────────────────

DET_MODEL_DIR = os.path.join(BASE_DIR, "det", "ch_PP-OCRv3_det_infer")
REC_MODEL_DIR = os.path.join(BASE_DIR, "rec", "korean_PP-OCRv3_mobile_rec_infer")
TABLE_MODEL_DIR = os.path.join(BASE_DIR, "table", "en_ppocr_mobile_v2.0_table_structure_infer")

# 한국어 모델은 내부에 dict 포함 → None
REC_CHAR_DICT = None
TABLE_CHAR_DICT = None

# ─── 입력 및 출력 파일/경로 설정 ────────────────────

IMG_PATH = r"D:\Receipt_Hospital\table.png"
OUTPUT_DIR = r"D:\Receipt_Hospital\output_simple"
JSON_FILENAME = "receipt_table.json"

# 출력 디렉토리 생성
os.makedirs(OUTPUT_DIR, exist_ok=True)
json_path = os.path.join(OUTPUT_DIR, JSON_FILENAME)


# ─── PPStructure 엔진 초기화 ──────────────────────
# use_gpu=False로 CPU 사용, show_log=False로 로그 출력 억제
# return_ocr_result_in_table=True로 테이블 내 OCR 결과 반환

engine = PPStructure(det_model_dir=DET_MODEL_DIR,
                    rec_model_dir=REC_MODEL_DIR,
                    table_model_dir=TABLE_MODEL_DIR,
                    table_char_dict_path=TABLE_CHAR_DICT,   # 한국어 모델은 None
                    use_gpu=False,
                    show_log=False,
                    return_ocr_result_in_table=True
                    )



# ─── 이미지 로드 및 전처리 (선택 사항: OCR 성능 개선 목적) ─────

img = cv2.imread(IMG_PATH)

# 이미지가 제대로 로드되었는지 확인

if img is None:
    print(f"오류: 이미지를 로드할 수 없습니다. 경로를 확인하세요: {IMG_PATH}")
    exit()


# ⚠️ OCR 성능 개선을 위한 이미지 전처리 예시 (선택 사항) ⚠️
# 원본 이미지를 그대로 사용하려면 아래 전처리 부분을 주석 처리하거나 'processed_img = img'로 설정
processed_img = img.copy()
# 원본 이미지 복사

# 1. 그레이스케일 변환

processed_img = cv2.cvtColor(processed_img, cv2.COLOR_BGR2GRAY)

# 2. 노이즈 제거 (가우시안 블러)
# 작은 커널(예: (3,3))을 사용하면 블러 효과를 줄여 글자 형태를 보존할 수 있습니다.

processed_img = cv2.GaussianBlur(processed_img, (3, 3), 0)

# 3. 이진화 (Adaptive Thresholding이 고정 임계값보다 다양한 조명에 유리)
# block_size와 C 값은 이미지에 따라 조절이 필요할 수 있습니다.

processed_img = cv2.adaptiveThreshold(processed_img, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

# 참고: 때로는 단순히 그레이스케일 이미지가 더 좋은 결과를 낼 수도 있습니다.
# processed_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) # 이진화 없이 그레이스케일만 사용하고 싶을 때

# ─── PPStructure 엔진 실행 ─────────────────────

# 전처리된 이미지를 엔진에 전달
results = engine(processed_img, return_ocr_result_in_table=True)

# ─── 결과 파싱 및 JSON 저장 ─────────────────────

if results and len(results) > 0:
    res = results[0]['res']
    bboxes = res['cell_bbox']
    ocr_res = res['rec_res']

    cells = []

    # 신뢰도 임계값 설정 (낮은 신뢰도의 결과를 필터링하거나 표시할 수 있음)
    CONFIDENCE_THRESHOLD = 0.4  # 이 값은 필요에 따라 조절하세요. 0.0은 모두 포함, 1.0은 완벽한 경우만

    for bbox, (text, score) in zip(bboxes, ocr_res):
        # 텍스트가 비어있지 않고, 신뢰도 임계값보다 높은 경우에만 추가
        # 신뢰도가 낮더라도 텍스트가 존재하는 경우를 포함하고 싶다면 조건 수정
        if text.strip() != "" and float(score) >= CONFIDENCE_THRESHOLD:
            cells.append({"text": text,
                            "confidence": float(score),
                            "bbox": bbox
                        })

        # 참고: 신뢰도가 0.0인데 텍스트가 빈 문자열인 경우는 자동으로 걸러짐

    # JSON 파일로 저장

    with open(json_path, "w", encoding="utf-8") as f:
        json.dump({"cells": cells}, f, ensure_ascii=False, indent=2)


    print(f"✅ 셀별 텍스트+신뢰도+바운딩박스 JSON 저장 완료: {json_path}")

    # 디버깅을 위한 추가 정보 출력 (선택 사항)

    if 'html' in res:
        print("\n--- 인식된 HTML 테이블 구조 (디버깅용) ---")
        print(res['html'])
    else:
        print("\n--- HTML 테이블 구조를 찾을 수 없습니다. ---")
else:
    print("❌ PPStructure 엔진에서 결과를 얻지 못했습니다. 입력 이미지 또는 모델 경로를 확인하세요.")
