# table/llm_merge.py
from collections import defaultdict
from typing import Callable, Dict, List, Tuple, Any
import copy

# ---- 유틸 ----
def bbox_union(b1, b2):
    return [min(b1[0], b2[0]), min(b1[1], b2[1]), max(b1[2], b2[2]), max(b1[3], b2[3])]

def uniq(seq):
    seen = set()
    out = []
    for x in seq:
        if x not in seen:
            out.append(x)
            seen.add(x)
    return out

# ---- 1) LLM에 보낼 컨텍스트 만들기 + 엔진 호출 래퍼 ----
def _build_cell_context(cell: dict, cells_by_id: Dict[int, dict]) -> dict:
    """셀 하나에 대한 LLM 컨텍스트(셀 텍스트, bbox, 이웃 텍스트들) 생성"""
    ctx = {
        "cid": cell["cid"],
        "bbox": cell["bbox"],
        "tokens": cell.get("tokens", []),
        "text": " ".join(cell.get("tokens", [])),
        "neighbors": {}
    }
    # 이웃 id 목록 필드명들
    for side in ("left", "right", "above", "below"):
        idx_key = f"{side}_idx"
        txt_key = f"{side}_text"
        ids = cell.get(idx_key, []) or []
        ctx["neighbors"][side] = {
            "cids": ids,
            "texts": cell.get(txt_key, []) or [],
            "detailed": [
                {
                    "cid": n_id,
                    "bbox": cells_by_id[n_id]["bbox"],
                    "text": " ".join(cells_by_id[n_id].get("tokens", [])),
                }
                for n_id in ids if n_id in cells_by_id
            ],
        }
    return ctx


def propose_corrections_with_engine(
    cell_neighbors: List[dict],
    engine: Callable[[dict], dict],
) -> Tuple[Dict[int, str], List[Dict[str, Any]]]:
    """
    각 셀의 컨텍스트를 LLM 엔진에 넣어 '보정 텍스트'와 '병합 지시'를 수집.

    Parameters
    ----------
    cell_neighbors : List[dict]
        update_cells_with_neighbors(...) 결과 리스트(각 셀 dict에 cid/bbox/tokens 및 *_idx, *_text 포함)
    engine : Callable
        engine(context) -> dict
        반환 예:
        {
          "cid": 12,
          "corrected_text": "급여 본인부담",
          "merge": {
             "target_cids": [13, 14],     # 병합 대상
             "reason": "세로로 분절된 헤더",
             "order": "x_then_y"          # 선택: 병합 순서 힌트
          }
        }
        또는 merge 키 없으면 병합 없음.

    Returns
    -------
    corrected_texts : Dict[cid, corrected_text]
    merge_plan : List[{"group": [cids...], "anchor": int, "reason": str}]
        - group: 함께 병합할 셀 id 묶음(앵커 포함)
        - anchor: 최종 대표 cid(가능하면 원래의 기준 셀)
    """
    cells_by_id = {c["cid"]: c for c in cell_neighbors}
    corrected_texts: Dict[int, str] = {}
    groups: Dict[int, List[int]] = {}  # anchor -> group cids
    reasons: Dict[int, str] = {}

    for cell in cell_neighbors:
        ctx = _build_cell_context(cell, cells_by_id)
        # --- 여기서 LLM 엔진 호출 (가정) ---
        resp = engine(ctx) or {}
        cid = cell["cid"]
        # 텍스트 보정
        if "corrected_text" in resp and resp["corrected_text"] is not None:
            corrected_texts[cid] = resp["corrected_text"]

        # 병합 지시
        mg = resp.get("merge")
        if mg and isinstance(mg.get("target_cids"), list) and len(mg["target_cids"]) > 0:
            anchor = cid  # 기본적으로 현재 셀을 앵커로 사용
            group = uniq([cid] + [t for t in mg["target_cids"] if t in cells_by_id])
            if len(group) > 1:
                groups[anchor] = group
                reasons[anchor] = mg.get("reason", "")

    # 앵커가 겹치거나 그룹이 겹치면 union 처리
    # 간단한 union: 서로 교집합이 있으면 합침
    anchors = list(groups.keys())
    merged = True
    while merged:
        merged = False
        for i in range(len(anchors)):
            for j in range(i + 1, len(anchors)):
                a, b = anchors[i], anchors[j]
                if a not in groups or b not in groups:
                    continue
                if set(groups[a]) & set(groups[b]):
                    groups[a] = uniq(groups[a] + groups[b])
                    reasons[a] = reasons.get(a) or reasons.get(b, "")
                    del groups[b]
                    anchors.remove(b)
                    merged = True
                    break
            if merged:
                break

    merge_plan = []
    for anchor, grp in groups.items():
        merge_plan.append({"group": grp, "anchor": anchor, "reason": reasons.get(anchor, "")})

    return corrected_texts, merge_plan


# ---- 2) 병합 계획을 적용하여 셀/이웃 업데이트 ----
def merge_cells_by_plan(
    cell_neighbors: List[dict],
    merge_plan: List[Dict[str, Any]],
    corrected_texts: Dict[int, str] = None,
    recompute_neighbors_fn: Callable[[List[dict]], List[dict]] = None,
) -> Tuple[List[dict], Dict[int, int]]:
    """
    병합 계획에 따라 셀을 병합하고( bbox/토큰/텍스트 ), cid 재매핑 후 이웃정보를 재계산.

    Parameters
    ----------
    cell_neighbors : List[dict]
        현재 셀 리스트(각 dict: cid, bbox, tokens, *_idx, *_text)
    merge_plan : List[{"group": [cids...], "anchor": int, "reason": str}]
    corrected_texts : Dict[int, str]
        엔진이 제안한 보정 텍스트(셀 단위). 병합 셀의 텍스트 결정에도 사용.
    recompute_neighbors_fn : Callable
        이웃 재계산 함수. 없으면 이웃은 그대로 두되, 참조 불일치가 생길 수 있으므로 보통 제공을 권장.
        예: table.cell.update_cells_with_neighbors

    Returns
    -------
    new_cells : List[dict]
        병합/보정 반영된 새로운 셀 리스트
    id_map : Dict[int, int]
        old_cid -> new_cid 매핑
    """
    corrected_texts = corrected_texts or {}

    # 1) 앵커 중심으로 병합 실행
    by_id = {c["cid"]: copy.deepcopy(c) for c in cell_neighbors}
    used = set()
    id_map = {}  # old -> new

    # 미리 모든 cid를 자기 자신으로 매핑
    for cid in by_id.keys():
        id_map[cid] = cid

    # 병합 그룹 처리
    for item in merge_plan:
        group = uniq(item["group"])
        if len(group) <= 1:
            continue
        anchor = item.get("anchor", group[0])
        if anchor not in group:
            group = [anchor] + group

        # 앵커 셀 시작
        if anchor not in by_id:
            # 앵커가 이미 병합으로 소실된 경우: 그룹 내 남아있는 id 아무거나 새 앵커로
            anchor = next((g for g in group if g in by_id), None)
            if anchor is None:
                continue

        base = by_id[anchor]
        # 1) bbox/토큰 병합
        for cid in group:
            if cid == anchor or cid not in by_id:
                continue
            other = by_id[cid]
            base["bbox"] = bbox_union(base["bbox"], other["bbox"])
            base["tokens"] = (base.get("tokens", []) or []) + (other.get("tokens", []) or [])
            used.add(cid)
            # id 매핑: 병합된 cid는 앵커로 이동
            id_map[cid] = anchor
            # 삭제 예약
            del by_id[cid]

        # 2) 텍스트(토큰) 정리
        # 엔진이 앵커에 대해 corrected_text를 줬다면 그걸 우선.
        if anchor in corrected_texts:
            base["tokens"] = [corrected_texts[anchor]]
        else:
            # 그룹 내에 corrected_text가 여럿이면 우선 앵커 것, 없으면 공백/중복 정리
            texts = []
            for cid in group:
                t = corrected_texts.get(cid)
                if t:
                    texts.append(t)
            if texts:
                base["tokens"] = [" ".join(uniq(texts))]
            else:
                # 중복 토큰 제거 및 간단 결합
                joined = " ".join(uniq([t for t in base.get("tokens", []) if t]))
                base["tokens"] = [joined] if joined else []

        # 이웃 필드들은 재계산 단계에서 다시 채운다.
        for k in list(base.keys()):
            if k.endswith("_idx") or k.endswith("_text"):
                base.pop(k, None)

    # 2) 병합에 참여하지 않은 나머지 셀들도 corrected_texts 반영
    for cid, cell in by_id.items():
        if cid in used:
            continue
        if cid in corrected_texts and corrected_texts[cid] is not None:
            cell["tokens"] = [corrected_texts[cid]]

        # 기존 이웃 필드 제거 (재계산 예정)
        for k in list(cell.keys()):
            if k.endswith("_idx") or k.endswith("_text"):
                cell.pop(k, None)

    # 3) 새로운 셀 리스트 구성
    new_cells = list(by_id.values())

    # 4) 이웃 재계산
    if recompute_neighbors_fn is not None:
        new_cells = recompute_neighbors_fn(new_cells)

    # 5) id_map 정리: 병합이 없던 cid도 자기 자신으로 매핑 유지
    for cell in new_cells:
        id_map[cell["cid"]] = cell["cid"]

    return new_cells, id_map
