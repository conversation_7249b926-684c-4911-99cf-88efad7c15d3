import json


def build_llm_prompt(context: dict) -> str:
    """
    LLM에 줄 단일 문자열 프롬프트.
    messages가 아닌 단일 prompt를 쓰므로 langchain LLMChain과 호환.
    """
    def fmt_neighbors(side):
        ns = context["neighbors"].get(side, {})
        rows = []
        for d in ns.get("detailed", []):
            rows.append(f"- cid:{d.get('cid')} text:{d.get('text','').strip()} bbox:{d.get('bbox')}")
        return "\n".join(rows) if rows else "- (none)"
    
    cell = f"cid:{context['cid']} text:{(context.get('text') or '').strip()} bbox:{context['bbox']}"
    above = fmt_neighbors("above")
    below = fmt_neighbors("below")
    left  = fmt_neighbors("left")
    right = fmt_neighbors("right")

    return f"""당신은 OCR 표 셀 보정 및 병합 판단 전문가입니다.
문서 유형: 병원 영수증(진료료/입원료/영상진단료/방사선치료료/치료재료대/조제료/약품비/기타, 급여/비급여/선택진료료, 전액/본인부담/공단부담 등 다단 헤더가 존재할 수 있음)

[요청]
1) 아래 셀의 텍스트를 자연스러운 한국어로 보정(오타/공백/전각반각/자모분리 교정).
2) 이 셀이 상/하/좌/우로 잘못 분절된 경우(같은 행/열에서 의미가 이어지는 조각), 병합 대상 셀 cid를 제안.
   - 수직 분절: 같은 '열'에서 위/아래 조각이 같은 개념을 구성
   - 수평 분절: 같은 '행'에서 좌/우 조각이 같은 개념을 구성
   - 병합은 '현재 셀을 대표(anchor)'로 유지한다고 가정

[출력 JSON 스키마]
{{
  "cid": <int>,  // 현재 셀 id
  "corrected_text": "<str>",  // 보정 텍스트(없으면 원문 유지)
  "merge": {{
     "target_cids": [<int>, ...],  // 병합 대상(현재 셀 제외)
     "reason": "<짧은 사유>",       // 예: "vertical split suspected"
     "axis": "x|y|both"            // 수평/수직/둘다
  }} | null
}}

[판단 가이드]
- 같은 행(수평): bbox의 y범위가 충분히 겹치면 같은 행
- 같은 열(수직): bbox의 x범위가 충분히 겹치면 같은 열
- 라벨/머리글 조각(예: "급","여", "(선택)","진료료"): 서로 이어 붙이면 자연스러운 단어가 되면 병합 후보
- 숫자/금액 칸은 라벨과 병합하지 말 것
- 출력은 반드시 JSON 하나만 반환

[현재 셀]
{cell}

[이웃-위]
{above}

[이웃-아래]
{below}

[이웃-좌]
{left}

[이웃-우]
{right}
"""


def exaone_engine_wrap(llm, context: dict) -> dict:
    """
    llm: ExaoneLLM 인스턴스 (또는 .invoke(str)->str 로 동작하는 LLM)
    """
    prompt = build_llm_prompt(context)
    raw = llm(prompt)  # LangChain LLM은 __call__ 가능. 혹은 llm.invoke(prompt)
    # 방어적 추출: 코드블록/잡텍스트 안의 JSON 추출
    raw_str = raw.strip()
    # 가장 처음 나오는 { ... } 블록만 파싱
    start = raw_str.find("{")
    end = raw_str.rfind("}")
    if start >= 0 and end > start:
        raw_str = raw_str[start:end+1]
    try:
        data = json.loads(raw_str)
    except Exception:
        # 최소 계약 보장
        return {"cid": context["cid"], "corrected_text": context.get("text",""), "merge": None}

    # 계약 필드 정리
    cid = int(data.get("cid", context["cid"]))
    corrected = data.get("corrected_text", context.get("text",""))
    merge = data.get("merge")
    if merge and isinstance(merge, dict):
        tgt = [int(x) for x in merge.get("target_cids", []) if isinstance(x, (int, str)) and str(x).isdigit()]
        if not tgt:
            merge = None
        else:
            merge = {
                "target_cids": tgt,
                "reason": str(merge.get("reason", ""))[:120],
                "axis": merge.get("axis", "y")
            }
    else:
        merge = None

    return {"cid": cid, "corrected_text": corrected, "merge": merge}

