from typing import Optional, List
import requests
from config import API_KEY


class ExaoneLLM:
    endpoint_url:str = 'https://api.lgresearch.ai/v1/exaone'
    model_name: str = 'exaone-v3.5_2.4b-instruct'  # 선택 모델명

    def __init__(self, api_key: Optional[str] = None):
        if api_key:
            self.api_key = api_key

    def _call(self, prompt: str, stop: Optional[List[str]] = None) -> str:
        headers = {
            "x-api-key": self.api_key,
            'Content-Type': 'application/json'
        }

        payload = {
            'model': self.model_name,
            'messages': prompt,
            'stream': False
        }

        response = requests.post(self.endpoint_url, headers=headers, json=payload)
        response.raise_for_status()

        # 실제 응답 구조에 따라 아래 부분 수정 필요
        result = response.json()
        return result['choices'][0]['message']['content']

    @property
    def _llm_type(self) -> str:
        return 'exaone'


if __name__ == '__main__':
    llm = ExaoneLLM(api_key=API_KEY)   # 2000/day, 10/min, 1/sec
    response = llm('LG Exaone에 대해 설명해줘')
    print(response)