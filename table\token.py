

def is_ocr_mostly_inside_cell(cell_bbox: list, ocr_bbox: list, threshold=0.5) -> bool:
    """
    OCR 박스가 셀 박스 안에 일정 비율 이상 포함되어 있는지 판단합니다.
    """
    x0 = max(cell_bbox[0], ocr_bbox[0])
    y0 = max(cell_bbox[1], ocr_bbox[1])
    x1 = min(cell_bbox[2], ocr_bbox[2])
    y1 = min(cell_bbox[3], ocr_bbox[3])

    inter_area = max(0, x1 - x0) * max(0, y1 - y0)
    ocr_area = (ocr_bbox[2] - ocr_bbox[0]) * (ocr_bbox[3] - ocr_bbox[1])
    cell_area = (cell_bbox[2] - cell_bbox[0]) * (cell_bbox[3] - cell_bbox[1])

    return ((inter_area / ocr_area) >= threshold if ocr_area > 0 else False) or\
            ((inter_area / cell_area) >= threshold if cell_area > 0 else False)


def extract_tokens_in_table(table_bbox:list, ocr_result:list) -> list:
    """테이블 영역에 포함된 OCR 토큰들만 추출"""
    table_tokens = []
    
    if not ocr_result:
        return []
    
    min_x, min_y, max_x, max_y = table_bbox
    
    for i, token in enumerate(ocr_result):
        text, box, angle = token['text'], token['bbox'], token['angle']

        if len(box) >= 4:
            # 토큰의 중심점이 테이블 영역 내에 있는지 확인
            token_x0, token_y0, token_x1, token_y1 = box
            
            if min_x <= token_x0 and token_x1 <= max_x and min_y <= token_y0 and token_y1 <= max_y:
                table_tokens.append(token)
    
    return table_tokens


def assign_tokens_to_cells(cells, ocr_result) -> list:
    """토큰을 셀에 할당 - 각 token의 cell id 리스트 생성"""
    assigned_tokens_bbox = []
    missing_tokens = []
    missing_tokens_bbox = []
    for token in ocr_result:
        assigned = False
        for cell in cells:
            if is_ocr_mostly_inside_cell(cell['bbox'], token['bbox']):
                cell['tokens'].append(token['text'])
                assigned_tokens_bbox.append(token['bbox'])
                assigned = True
                break
        if assigned == False:
            missing_tokens.append(token)
            missing_tokens_bbox.append(token['bbox'])

    # TODO: global 변수는 class화 필요
    # cells = [cell['bbox'] for cell in cell_tokens]
    # draw_multiple_bbox_sets(PATH_DATA / file, [cells], PATH_OUTPUTS / file.stem / 'debug_assigned_tokens.png')
    print('# of missing tokens:', len(missing_tokens))
    return cells