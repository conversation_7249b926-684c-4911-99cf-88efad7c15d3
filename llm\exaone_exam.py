import requests


url = "https://api.lgresearch.ai/v1/exaone"
headers = {
    "Content-Type": "application/json; charset=utf-8",
    "x-api-key": "**********************************************************",
}
messages = [
    {"role": "system", "content": "당신은 의료비 영수증의 OCR결과를 보정하는 역할입니다."}
]

def ask_exaone(question:str):
    prompt = messages.copy()
    prompt.append({"role": "user", "content": question})

    payload = {"model": "exaone-v3.5_2.4b-instruct", "messages": messages, "stream": False}

    response = requests.post(url=url, json=payload, headers=headers)
    result = response.json()
    return result['choices'][0]['message']['content']

if __name__ == '__main__':
    question = "안녕?"
    resp = ask_exaone(question)
    print(resp)
