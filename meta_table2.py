import csv
import pandas as pd
from pathlib import Path
from table.cell import compute_guide_lines, snap_cells_to_guide_lines, fill_gaps, update_cells_with_neighbors
from table.cell import cluster_row_cells
from table.token import extract_tokens_in_table, assign_tokens_to_cells
from table.llm_merge import propose_corrections_with_engine, merge_cells_by_plan
from table.table_struct import TableStruct
from llm.mock import mock_engine
from llm.exaone import ExaoneLLM
from llm.common import exaone_engine_wrap
from utils import load_ppstructure_json, convert_type
from config import PATH_OUTPUTS



def process_ppstructure(ppjson_path, out_dir: Path, y_tol=5, x_tol=5):
    """PPStructure JSON → 테이블별 CSV + DataFrame 전처리용 파일 생성"""
    pp = load_ppstructure_json(ppjson_path)

    blocks = pp.get('parsing_res_list')
    tbl_cells = pp.get('table_res_list', [])
    tbl_cells = [[convert_type(boxes, int) for boxes in cells['cell_box_list']] for cells in tbl_cells]
    
    # overall_ocr_res에서 각종 정보 추출
    overall_ocr = pp.get('overall_ocr_res', {})
    ocr_angles = overall_ocr.get('textline_orientation_angles', [])
    ocr_texts = overall_ocr.get('rec_texts', [])
    ocr_bboxes = overall_ocr.get('rec_boxes', [])
    ocr_scores = overall_ocr.get('rec_scores', [])
    
    ocr_result = [{'text': text, 'bbox': bbox, 'angle': angle, 'score': score}\
                    for text, bbox, angle, score in zip(ocr_texts, ocr_bboxes, ocr_angles, ocr_scores)]
    print(f"Global OCR results: {len(ocr_result)} texts")
    
    results = []
    for tidx, cells in enumerate(tbl_cells):
        # 테이블 전체 영역 계산
        table_bbox = get_table_bbox(cells)
        if not table_bbox:
            print(f"Table {tidx}: No cells found")
            return None
        
        # int로 처리
        table_bbox = [int(coor) for coor in table_bbox]
        
        # 테이블 영역의 OCR 토큰들만 추출
        tbl_ocrs = extract_tokens_in_table(table_bbox, ocr_result)
        print(f"Table {tidx}: extracted {len(tbl_ocrs)} tokens from table area")
        
        tables = process_single_table(tidx, table_bbox, cells, tbl_ocrs, out_dir, y_tol, x_tol) 
        
        for result in tables:
            df = pd.read_csv(result['cells_csv'])
            # cell_info(df, 300)
        
            if result:
                results.append(result)
    
    return results


def process_single_table(tidx, table_bbox, cells, ocr_result, out_dir, y_tol=5, x_tol=5):
    """단일 테이블 처리"""

    # cell 구조체 초기화
    # 먼저 y 좌표(행) 기준으로 정렬한 후, 같은 행 안에서는 x 좌표(열) 기준으로 정렬합니다.
    # cells는 bbox의 list이므로, 각 bbox를 dict로 변환하여 정렬합니다.
    cells = sorted(cells, key=lambda bbox: (bbox[1], bbox[0]))
    sorted_cells = []
    for cid, bbox in enumerate(cells):
        sorted_cells.append({
            'cid': cid,
            'bbox': bbox,
            'tokens': []
        })
    cells = sorted_cells

    # 0) Cell 병합 및 빈부분으로 확대 / cell들 좌표 정렬?
    x_lines, y_lines = compute_guide_lines(table_bbox, cells, x_tol, y_tol)
    cells = snap_cells_to_guide_lines(cells, x_lines, y_lines)
    
    # cells중 gap이 있는 부분을 채우기 위한 전처리
    cells = fill_gaps(cells, table_bbox)

    # 1) OCR 토큰을 셀에 할당
    cells_token = assign_tokens_to_cells(cells, ocr_result)

    # 4) 셀 간 neighbor 찾기
    cells_neighbors = update_cells_with_neighbors(cells_token)
 
    # 2) cell 보정 및 병합 판단
    # - LLM 기반 보정 및 병합 판단
    # - mock_engine은 간단한 규칙 기반 예시 엔진
    # - exaone_engine_wrap은 실제 Exaone LLM을 사용하는 래퍼 함수
    # - corrected_texts는 {cid: corrected_text} 형태로, 병합 계획은 {group: [...], anchor: cid} 형태로 반환됨
    # 1) 규칙기반으로만
    # corrected_texts, merge_plan = propose_corrections_with_engine(cells_neighbors, engine=mock_engine)

    # 2) LLM 기반으로
    llm = ExaoneLLM()
    corrected_texts, merge_plan = propose_corrections_with_engine(
                                        cells_neighbors,
                                        engine=lambda ctx: exaone_engine_wrap(llm, ctx)
                                    )

    # 3) 병합 계획 적용 + 이웃 재계산
    merged_cells, id_map = merge_cells_by_plan(
                                cells_neighbors,
                                merge_plan,
                                corrected_texts=corrected_texts,
                                recompute_neighbors_fn=update_cells_with_neighbors,  # 이미 있는 함수 재사용
                            )
    # old_id = 2
    # new_id = id_map[old_id]  # 1
    # print(f"셀 {old_id} 는 병합 후 {new_id} 로 이동")

    # merged_cells: your list of dicts with keys like cid, bbox, tokens, left_idx/right_idx/above_idx/below_idx (optional)
    ts = TableStruct(merged_cells, tol=6)
    print("shape:", ts.shape())
    print("R0 text:", ts.row_text(0))
    print("C0 text:", ts.col_text(0))

    # regex search
    for r, c, item in ts.find(r"진료|입원|본인부담"):
        print(r, c, item["text"], item["row_span"], item["col_span"])

    # pandas dataframe
    df = ts.to_dataframe()  # requires pandas
    print(df.head())

    # neighbors of a specific cell id
    print(ts.neighbors_by_cid(123))


    # (선택) 병합 후 row 그룹 재구성
    cid_rows = cluster_row_cells(merged_cells, y_tol=5)

    
    # 5) CSV 저장
    # tokens_csv = save_tokens_csv(out_dir, tidx, token_texts, token_boxes, token_cells, token_scores, token_angles)
    csv_path = out_dir / f"table_{tidx}_cells.csv"
    save_cells_csv(csv_path, tidx, cells_neighbors, cid_rows)
    
    return {
        'table_idx': tidx,
        'cells': cells,
        'cell_updated': cells_neighbors,
        'cells_csv': csv_path
    }


def merge_cells(cells, axis='y', threshold=0.8):
    """
    셀들을 병합합니다.
    axis: 'x' 또는 'y' - 병합 기준 축
    threshold: 병합 기준 비율 (0~1)
    """
    merged_cells = []




def merge_bbox(bbox1, bbox2):
    """
    두 bbox를 하나의 bbox로 병합.
    """
    x0 = min(bbox1[0], bbox2[0])
    y0 = min(bbox1[1], bbox2[1])
    x1 = max(bbox1[2], bbox2[2])
    y1 = max(bbox1[3], bbox2[3])
    return [x0, y0, x1, y1]


def save_cells_csv(csv_path, tidx, cell_neighbors, cid_rows=None):
    """셀별 정보를 CSV로 저장 (cell_neighbors를 입력으로 사용)
    cid_rows가 제공되면 그 순서대로 csv에 기록합니다.
    """
    csv_path.parent.mkdir(parents=True, exist_ok=True)

    with csv_path.open('w', newline='', encoding='utf-8-sig') as f:
        w = csv.writer(f)
        w.writerow([
            'table_idx', 'cell_id', 'x0', 'y0', 'x1', 'y1', 'tokens',
            'left_idx', 'right_idx', 'above_idx', 'below_idx',
            'left_text', 'right_text', 'above_text', 'below_text'
        ])

        # Map cell id to cell info for quick lookup.
        cell_dict = {cell.get('cid'): cell for cell in cell_neighbors}
        for row in cid_rows:
            for cid in row:
                cell = cell_dict.get(cid)
                if cell is None:
                    continue
                bbox = cell.get('bbox', [None, None, None, None])
                x0, y0, x1, y1 = bbox
                tokens = ' '.join(cell.get('tokens', []))
                left_ids = cell.get('left_idx', [])
                right_ids = cell.get('right_idx', [])
                above_ids = cell.get('above_idx', [])
                below_ids = cell.get('below_idx', [])
                left_text = '|'.join(cell.get('left_text', []))
                right_text = '|'.join(cell.get('right_text', []))
                above_text = '|'.join(cell.get('above_text', []))
                below_text = '|'.join(cell.get('below_text', []))

                w.writerow([
                    tidx,
                    cid,
                    round(x0) if x0 is not None else '',
                    round(y0) if y0 is not None else '',
                    round(x1) if x1 is not None else '',
                    round(y1) if y1 is not None else '',
                    tokens,
                    ','.join(map(str, left_ids)),
                    ','.join(map(str, right_ids)),
                    ','.join(map(str, above_ids)),
                    ','.join(map(str, below_ids)),
                    left_text,
                    right_text,
                    above_text,
                    below_text
                ])

    print(f"Saved cells: {csv_path}")
    return csv_path


def get_table_bbox(cells) -> tuple:
    """테이블의 전체 바운딩박스 계산"""
    if not cells:
        return None
    
    min_x = min(cell[0] for cell in cells)
    min_y = min(cell[1] for cell in cells)
    max_x = max(cell[2] for cell in cells)
    max_y = max(cell[3] for cell in cells)
    
    return (min_x, min_y, max_x, max_y)


file = None
if __name__ == '__main__':
    # 예시 사용
    file = Path('receipt5.jpg')
    output_folder = PATH_OUTPUTS / file.stem
    ocr_json = output_folder / f'{file.stem}_res.json'
    process_ppstructure(ocr_json, output_folder)
    
