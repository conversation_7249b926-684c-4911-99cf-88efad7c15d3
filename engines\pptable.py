from pathlib import Path
from paddleocr import PPStructureV3


class PPTable:
    CONFIG_FILE ="ppstructure_config.yaml"

    def __init__(self,
                model_dir:Path,
                use_doc_unwarping=False,
                device='cpu'
                ):
        
        # --- 텍스트 검출 & 인식
        text_recognition_model_name='korean_PP-OCRv5_mobile_rec'
        text_detection_model_name='PP-OCRv5_server_det'

        # --- Layout (문서 블록/레이아웃) 감지
        layout_detection_model_name="PP-DocLayout_plus-L"

        region_detection_model_name="PP-DocBlockLayout"

        # --- 문서 전처리 (방향 분류 + 언워핑)
        doc_orientation_classify_model_name="PP-LCNet_x1_0_doc_ori"

        doc_unwarping_model_name="UVDoc"

        # --- 텍스트 라인 방향
        textline_orientation_model_name="PP-LCNet_x1_0_textline_ori"

        # --- (필요 시) 표(table), 수식, 차트, 인장 등 추가 모듈
        table_classification_model_name="PP-LCNet_x1_0_table_cls"
        seal_text_detection_model_name="PP-OCRv4_server_seal_det"
        wired_table_structure_recognition_model_name="SLANeXt_wired"
        wired_table_structure_recognition_model_name="SLANeXt_wired"
        wired_table_cells_detection_model_name="RT-DETR-L_wired_table_cell_det"
        wireless_table_structure_recognition_model_name="SLANet_plus"
        wireless_table_cells_detection="RT-DETR-L_wireless_table_cell_det"
        formula_recognition_model_name="PP-FormulaNet_plus-L"
        chart_recognition_model_name="PP-Chart2Table"


        self.engine = PPStructureV3(
                                    text_recognition_model_name=text_recognition_model_name,
                                    text_recognition_model_dir=str(model_dir / text_recognition_model_name),
                                    text_detection_model_name=text_detection_model_name,
                                    text_detection_model_dir=str(model_dir / text_detection_model_name),
                                    layout_detection_model_name=layout_detection_model_name,
                                    layout_detection_model_dir=str(model_dir / layout_detection_model_name),
                                    region_detection_model_name=region_detection_model_name,
                                    region_detection_model_dir=str(model_dir / region_detection_model_name),
                                    doc_orientation_classify_model_name=doc_orientation_classify_model_name,
                                    doc_orientation_classify_model_dir=str(model_dir / doc_orientation_classify_model_name),
                                    doc_unwarping_model_name=doc_unwarping_model_name,
                                    doc_unwarping_model_dir=str(model_dir / doc_unwarping_model_name),
                                    textline_orientation_model_name=textline_orientation_model_name,
                                    textline_orientation_model_dir=str(model_dir / textline_orientation_model_name), 
                                    table_classification_model_name=table_classification_model_name,
                                    table_classification_model_dir=str(model_dir / table_classification_model_name),
                                    seal_text_detection_model_name=seal_text_detection_model_name,
                                    seal_text_detection_model_dir=str(model_dir / seal_text_detection_model_name),
                                    wired_table_structure_recognition_model_name=wired_table_structure_recognition_model_name,
                                    wired_table_structure_recognition_model_dir=str(model_dir / wired_table_structure_recognition_model_name),
                                    wired_table_cells_detection_model_name=wired_table_cells_detection_model_name,
                                    wired_table_cells_detection_model_dir=str(model_dir / wired_table_cells_detection_model_name),
                                    wireless_table_structure_recognition_model_name=wireless_table_structure_recognition_model_name,
                                    wireless_table_structure_recognition_model_dir=str(model_dir / wireless_table_structure_recognition_model_name),
                                    wireless_table_cells_detection_model_name=wireless_table_cells_detection,
                                    wireless_table_cells_detection_model_dir=str(model_dir / wireless_table_cells_detection),
                                    formula_recognition_model_name=formula_recognition_model_name,
                                    formula_recognition_model_dir=str(model_dir / formula_recognition_model_name),
                                    chart_recognition_model_name=chart_recognition_model_name,
                                    chart_recognition_model_dir=str(model_dir / chart_recognition_model_name),

                                    use_doc_unwarping=use_doc_unwarping,
                                    device=device
                                    )

    def predict(self, input):
        return self.engine.predict(input)
    

if __name__ == '__main__':
    # 최초 한 번만 실행하면 기본 파이프라인 설정이 ppstructure_config.yaml로 저장됩니다.
    text_recognition_model_name='korean_PP-OCRv5_mobile_rec'

    pipeline = PPStructureV3(
        use_doc_orientation_classify=True,
        use_doc_unwarping=True,
        use_region_detection=True,
        text_recognition_model_name=text_recognition_model_name
    )
    pipeline.export_paddlex_config_to_yaml("ppstructure_config.yaml")
