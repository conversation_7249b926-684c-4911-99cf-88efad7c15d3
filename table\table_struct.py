from __future__ import annotations

from dataclasses import dataclass
from typing import List, Tu<PERSON>, Dict, Any, Optional, Iterable
import math
import re

try:
    import pandas as pd  # optional
except Exception:
    pd = None


@dataclass
class Cell:
    cid: int
    bbox: List[int]               # [x0, y0, x1, y1]
    tokens: List[str]             # recognized or corrected tokens
    # neighbors are optional; not required for struct building
    left_idx: List[int] = None
    right_idx: List[int] = None
    above_idx: List[int] = None
    below_idx: List[int] = None

    @property
    def text(self) -> str:
        return " ".join(self.tokens or []).strip()

    @property
    def x0(self): return self.bbox[0]

    @property
    def y0(self): return self.bbox[1]

    @property
    def x1(self): return self.bbox[2]

    @property
    def y1(self): return self.bbox[3]

    def intersects_h(self, other:'Cell') -> int:
        """horizontal overlap length (x-axis)"""
        return max(0, min(self.x1, other.x1) - max(self.x0, other.x0))

    def intersects_v(self, other:'Cell') -> int:
        """vertical overlap length (y-axis)"""
        return max(0, min(self.y1, other.y1) - max(self.y0, other.y0))


def _cluster_lines(coords: List[int], tol: int) -> List[int]:
    """Cluster 1D coordinates into band edges with tolerance. Return sorted unique centers."""
    if not coords:
        return []
    coords = sorted(coords)
    bands = [[coords[0]]]
    for c in coords[1:]:
        if abs(c - bands[-1][-1]) <= tol:
            bands[-1].append(c)
        else:
            bands.append([c])
    centers = [int(sum(b)/len(b)) for b in bands]
    return centers


def _band_index(value: int, bands: List[int]) -> int:
    """Return closest band index for value."""
    if not bands:
        return -1
    best_i, best_d = 0, float("inf")
    for i, b in enumerate(bands):
        d = abs(value - b)
        if d < best_d:
            best_i, best_d = i, d
    return best_i


class TableStruct:
    """
    Build a grid-like structure from merged_cells (each having bbox + tokens).
    Allows:
      - get(row, col) / set(row, col)
      - to_dataframe() (requires pandas)
      - find(text or regex) -> list of (row, col, cell)
      - neighbors(cid) -> dict
      - row_text(row) / col_text(col)
    Notes:
      - We infer row/col bands from cell top/left edges with a tolerance.
      - Spanning cells are supported: we record (row_span, col_span).
      - Empty slots are None by default.
    """
    def __init__(self, merged_cells: Iterable[Dict[str, Any]], tol: int = 6):
        # Normalize cells
        self.cells: List[Cell] = []
        for c in merged_cells:
            self.cells.append(Cell(
                cid=int(c.get("cid")),
                bbox=list(map(int, c.get("bbox"))),
                tokens=list(c.get("tokens", [])),
                left_idx=c.get("left_idx") or [],
                right_idx=c.get("right_idx") or [],
                above_idx=c.get("above_idx") or [],
                below_idx=c.get("below_idx") or [],
            ))

        # build row/col bands
        y_tops = [c.y0 for c in self.cells]
        x_lefts = [c.x0 for c in self.cells]
        self.row_bands = _cluster_lines(y_tops, tol=tol)
        self.col_bands = _cluster_lines(x_lefts, tol=tol)

        self.n_rows = len(self.row_bands)
        self.n_cols = len(self.col_bands)

        # grid: store dict for spans; None for empty
        self.grid: List[List[Optional[Dict[str, Any]]]] = [
            [None for _ in range(self.n_cols)] for _ in range(self.n_rows)
        ]

        # place cells
        for c in self.cells:
            r0 = _band_index(c.y0, self.row_bands)
            c0 = _band_index(c.x0, self.col_bands)
            # infer spans
            r1 = r0
            while r1 + 1 < self.n_rows and self.row_bands[r1 + 1] < c.y1:
                r1 += 1
            c1 = c0
            while c1 + 1 < self.n_cols and self.col_bands[c1 + 1] < c.x1:
                c1 += 1

            item = {
                "cid": c.cid,
                "bbox": c.bbox,
                "text": c.text,
                "row_span": r1 - r0 + 1,
                "col_span": c1 - c0 + 1,
            }
            self.grid[r0][c0] = item

        self._by_cid = {c.cid: c for c in self.cells}

    # ---- basic accessors ----
    def shape(self) -> Tuple[int, int]:
        return self.n_rows, self.n_cols

    def get(self, row:int, col:int) -> Optional[Dict[str, Any]]:
        if 0 <= row < self.n_rows and 0 <= col < self.n_cols:
            return self.grid[row][col]
        return None

    def text_at(self, row:int, col:int) -> str:
        cell = self.get(row, col)
        return cell["text"] if cell else ""

    def row_text(self, row:int) -> str:
        return " | ".join([self.text_at(row, c) for c in range(self.n_cols) if self.text_at(row, c)])

    def col_text(self, col:int) -> str:
        return " | ".join([self.text_at(r, col) for r in range(self.n_rows) if self.text_at(r, col)])

    # ---- search ----
    def find(self, pattern:str, flags:int=0) -> List[Tuple[int,int,Dict[str,Any]]]:
        """Find cells whose text matches regex pattern."""
        rx = re.compile(pattern, flags)
        hits = []
        for r in range(self.n_rows):
            for c in range(self.n_cols):
                item = self.grid[r][c]
                if item and rx.search(item["text"] or ""):
                    hits.append((r, c, item))
        return hits

    def find_exact(self, text:str) -> List[Tuple[int,int,Dict[str,Any]]]:
        t = text.strip()
        return [(r,c,item) for r in range(self.n_rows) for c in range(self.n_cols)
                if (item:=self.grid[r][c]) and (item["text"].strip()==t)]

    # ---- neighbors ----
    def neighbors_by_cid(self, cid:int) -> Dict[str, List[int]]:
        c = self._by_cid.get(cid)
        if not c:
            return {"left":[], "right":[], "above":[], "below":[]}
        return {
            "left": c.left_idx or [],
            "right": c.right_idx or [],
            "above": c.above_idx or [],
            "below": c.below_idx or [],
        }

    # ---- conversion ----
    def to_dataframe(self):
        if pd is None:
            raise ImportError("pandas is not available")
        data = []
        for r in range(self.n_rows):
            row = []
            for c in range(self.n_cols):
                item = self.grid[r][c]
                row.append(item["text"] if item else "")
            data.append(row)
        return pd.DataFrame(data)

    # ---- convenience helpers ----
    def map_by_header(self, header_row:int=0) -> Dict[str, List[str]]:
        headers = [self.text_at(header_row, c) for c in range(self.n_cols)]
        out: Dict[str, List[str]] = {}
        for c, h in enumerate(headers):
            if not h:
                continue
            out[h] = [self.text_at(r, c) for r in range(header_row+1, self.n_rows)]
        return out
