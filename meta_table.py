import json
import csv
from pathlib import Path
from config import PATH_OUTPUTS, PATH_DATA


def load_ppstructure_json(ppjson_path):
    """PPStructure JSON 파일 로드"""
    return json.loads(Path(ppjson_path).read_text(encoding='utf-8'))


def assign_tokens_to_cells(cells, texts, boxes) -> list:
    """토큰을 셀에 할당 - 각 token의 cell id 리스트 생성"""
    token_cells = []
    for text, box in zip(texts, boxes):
        x0, y0, x1, y1 = box
        cx, cy = (x0+x1)/2, (y0+y1)/2
        assigned = None
        for cid, (cx0,cy0,cx1,cy1) in enumerate(cells):
            if cx0 <= cx <= cx1 and cy0 <= cy <= cy1:
                assigned = cid
                break
        token_cells.append(assigned)
    return token_cells


def cluster_cells_to_grid(cells, y_tol=5, x_tol=5):
    """셀을 그리드(row/col)로 클러스터링하고 좌표 순서대로 정렬"""
    # 1단계: y_center 기준으로 row 그룹 만들기
    row_groups = []  # 각 그룹은 [cell_ids] 리스트
    
    for cid, (cx0,cy0,cx1,cy1) in enumerate(cells):
        y_center = (cy0+cy1)/2
        
        # 기존 그룹 중 y_tol 범위 내에 있는지 확인
        assigned = False
        for group_idx, group in enumerate(row_groups):
            # 그룹의 대표 y 좌표 계산
            group_y = sum((cells[c][1] + cells[c][3])/2 for c in group) / len(group)
            if abs(group_y - y_center) <= y_tol:
                row_groups[group_idx].append(cid)
                assigned = True
                break
        
        if not assigned:
            row_groups.append([cid])
    
    # 2단계: row 그룹들을 y 좌표 순서대로 정렬 (위에서 아래로: 작은 y부터)
    row_groups_with_y = []
    for group in row_groups:
        avg_y = sum((cells[c][1] + cells[c][3])/2 for c in group) / len(group)
        row_groups_with_y.append((avg_y, group))
    
    # y 좌표가 작을수록 위쪽이므로 오름차순 정렬
    row_groups_with_y.sort(key=lambda x: x[0])
    sorted_row_groups = [group for _, group in row_groups_with_y]
    
    # 3단계: 정렬된 순서로 row 번호 할당 (위에서부터 0, 1, 2, ...)
    cell_row = {}
    for row_idx, group in enumerate(sorted_row_groups):
        for cid in group:
            cell_row[cid] = row_idx
    
    # 4단계: 각 row 내에서 col 그룹 만들고 정렬
    cell_col = {}
    for row_idx, row_group in enumerate(sorted_row_groups):
        # 이 row의 셀들에 대해 col 그룹화
        col_groups = []
        
        for cid in row_group:
            x_center = (cells[cid][0] + cells[cid][2]) / 2
            
            # 기존 col 그룹 중 x_tol 범위 내에 있는지 확인
            assigned = False
            for col_idx, col_group in enumerate(col_groups):
                group_x = sum((cells[c][0] + cells[c][2])/2 for c in col_group) / len(col_group)
                if abs(group_x - x_center) <= x_tol:
                    col_groups[col_idx].append(cid)
                    assigned = True
                    break
            
            if not assigned:
                col_groups.append([cid])
        
        # col 그룹들을 x 좌표 순서대로 정렬 (왼쪽에서 오른쪽으로: 작은 x부터)
        col_groups_with_x = []
        for group in col_groups:
            avg_x = sum((cells[c][0] + cells[c][2])/2 for c in group) / len(group)
            col_groups_with_x.append((avg_x, group))
        
        # x 좌표가 작을수록 왼쪽이므로 오름차순 정렬
        col_groups_with_x.sort(key=lambda x: x[0])
        
        # 정렬된 순서로 col 번호 할당 (왼쪽부터 0, 1, 2, ...)
        for col_idx, (_, col_group) in enumerate(col_groups_with_x):
            for cid in col_group:
                cell_col[cid] = col_idx
    
    return cell_row, cell_col


def find_cell_neighbors(cells, cell_row, cell_col):
    """셀 간 neighbor (row/col ±1) 찾기"""
    def find_cell(rr,cc):
        for x in cell_row:
            if cell_row[x]==rr and cell_col[x]==cc:
                return x
        return None
    
    neighbors = {}
    for cid in range(len(cells)):
        r,c = cell_row[cid], cell_col[cid]
        neighbors[cid] = {
            'left':   find_cell(r, c-1),
            'right':  find_cell(r, c+1),
            'above':  find_cell(r-1, c),
            'below':  find_cell(r+1, c),
        }
    return neighbors


def save_tokens_csv(out_dir, tidx, texts, boxes, token_cells, scores=None, angles=None):
    """토큰별 정보를 CSV로 저장 (검증용)"""
    out_dir.mkdir(parents=True, exist_ok=True)
    csv_path = out_dir / f"table_{tidx}_tokens.csv"
    
    with csv_path.open('w', newline='', encoding='utf-8-sig') as f:
        w = csv.writer(f)
        w.writerow(['token_id', 'text', 'bbox', 'assigned_cell_id', 'confidence', 'angle'])
        
        for tid, (text, box, cell_id) in enumerate(zip(texts, boxes, token_cells)):
            x0, y0, x1, y1 = box
            score = scores[tid] if scores and tid < len(scores) else None
            angle = angles[tid] if angles and tid < len(angles) else None
            w.writerow([tid, text, f"[{round(x0)},{round(y0)},{round(x1)},{round(y1)}]", cell_id, score, angle])
    
    print(f"Saved tokens: {csv_path}")
    return csv_path


def save_cells_csv(out_dir, tidx, cells, cell_row, cell_col, neighbors, cell_text):
    """셀별 정보를 CSV로 저장"""
    out_dir.mkdir(parents=True, exist_ok=True)
    csv_path = out_dir / f"table_{tidx}_cells.csv"
    
    # 셀들을 row, col 순서대로 정렬
    cell_data = []
    for cid in range(len(cells)):
        r, c = cell_row[cid], cell_col[cid]
        x0, y0, x1, y1 = cells[cid]
        nb = neighbors[cid]
        cell_data.append((r, c, cid, x0, y0, x1, y1, cell_text[cid], nb))
    
    # row 먼저, 그 다음 col 순서로 정렬
    cell_data.sort(key=lambda x: (x[0], x[1]))  # (row, col) 순서
    
    with csv_path.open('w', newline='', encoding='utf-8-sig') as f:
        w = csv.writer(f)
        w.writerow([
            'table_idx','cell_id','row','col','cell_bbox',
            'texts','left_id','right_id','above_id','below_id'
        ])
        
        for r, c, cid, x0, y0, x1, y1, text, nb in cell_data:
            w.writerow([
                tidx, cid, r, c, f"[{round(x0)},{round(y0)},{round(x1)},{round(y1)}]",
                text,
                nb['left'], nb['right'], nb['above'], nb['below']
            ])
    
    print(f"Saved cells: {csv_path}")
    return csv_path


def extract_table_tokens(table_bbox, global_texts, global_boxes, global_angles, global_scores=None):
    """테이블 영역에 포함된 OCR 토큰들만 추출"""
    table_tokens = []
    
    if not global_texts or not global_boxes or not global_angles:
        return [], [], [], []
    
    min_x, min_y, max_x, max_y = table_bbox
    
    for i, (text, box, angle) in enumerate(zip(global_texts, global_boxes, global_angles)):
        if len(box) >= 4:
            # 토큰의 중심점이 테이블 영역 내에 있는지 확인
            token_cx = (box[0] + box[2]) / 2
            token_cy = (box[1] + box[3]) / 2
            
            if min_x <= token_cx <= max_x and min_y <= token_cy <= max_y:
                score = global_scores[i] if global_scores and i < len(global_scores) else None
                table_tokens.append((text, box, angle, score))
    
    if table_tokens:
        texts, boxes, angles, scores = zip(*table_tokens)
        return list(texts), list(boxes), list(angles), list(scores)
    else:
        return [], [], [], []


def get_table_bbox(cells):
    """테이블의 전체 바운딩박스 계산"""
    if not cells:
        return None
    
    min_x = min(cell[0] for cell in cells)
    min_y = min(cell[1] for cell in cells)
    max_x = max(cell[2] for cell in cells)
    max_y = max(cell[3] for cell in cells)
    
    return (min_x, min_y, max_x, max_y)


def process_single_table(tidx, tbl, out_dir, y_tol=5, x_tol=5, 
                        global_angles=None, global_texts=None, global_boxes=None, global_scores=None):
    """단일 테이블 처리"""
    cells = tbl['cell_box_list']
    
    # 테이블 전체 영역 계산
    table_bbox = get_table_bbox(cells)
    if not table_bbox:
        print(f"Table {tidx}: No cells found")
        return None
    
    # 테이블 영역의 OCR 토큰들만 추출
    token_texts, token_boxes, token_angles, token_scores = extract_table_tokens(
        table_bbox, global_texts, global_boxes, global_angles, global_scores
    )
    
    print(f"Table {tidx}: extracted {len(token_texts)} tokens from table area")
    print(f"Table {tidx}: {len([a for a in token_angles if a != 0.0])} tokens with non-zero angles")
    
    # 1) 토큰을 셀에 할당
    token_cells = assign_tokens_to_cells(cells, token_texts, token_boxes)
    
    # 2) 셀 그리드 클러스터링
    cell_row, cell_col = cluster_cells_to_grid(cells, y_tol, x_tol)
    
    # 3) 셀별 텍스트 그룹화 (OCR 토큰 기준)
    grouped = {cid: [] for cid in range(len(cells))}
    for text, cid in zip(token_texts, token_cells):
        if cid is not None:
            grouped[cid].append(text)
    cell_text = {cid: " ".join(grouped[cid]) for cid in grouped}
    
    # 4) 셀 간 neighbor 찾기
    neighbors = find_cell_neighbors(cells, cell_row, cell_col)
    
    # 5) CSV 저장
    tokens_csv = save_tokens_csv(out_dir, tidx, token_texts, token_boxes, token_cells, token_scores, token_angles)
    cells_csv = save_cells_csv(out_dir, tidx, cells, cell_row, cell_col, neighbors, cell_text)
    
    return {
        'table_idx': tidx,
        'cells': cells,
        'cell_row': cell_row,
        'cell_col': cell_col,
        'cell_text': cell_text,
        'neighbors': neighbors,
        'tokens_csv': tokens_csv,
        'cells_csv': cells_csv
    }


def process_ppstructure(ppjson_path, out_dir: Path, y_tol=5, x_tol=5):
    """PPStructure JSON → 테이블별 CSV + DataFrame 전처리용 파일 생성"""
    pp = load_ppstructure_json(ppjson_path)
    tables = pp.get('table_res_list', [])
    
    # overall_ocr_res에서 각도 정보 추출
    overall_ocr = pp.get('overall_ocr_res', {})
    global_angles = overall_ocr.get('textline_orientation_angles', [])
    global_texts = overall_ocr.get('rec_texts', [])
    global_boxes = overall_ocr.get('rec_boxes', [])
    global_scores = overall_ocr.get('rec_scores', [])
    
    print(f"Global OCR results: {len(global_texts)} texts, {len(global_angles)} angles")
    
    results = []
    for tidx, tbl in enumerate(tables):
        result = process_single_table(tidx, tbl, out_dir, y_tol, x_tol, 
                                    global_angles, global_texts, global_boxes, global_scores)
        if result:
            results.append(result)
    
    return results


if __name__ == '__main__':
    # 예시 사용
    file = Path('receipt5.jpg')
    output_folder = PATH_OUTPUTS / file.stem
    ocr_json = output_folder / f'{file.stem}_res.json'
    process_ppstructure(ocr_json, output_folder)
